[{"concept_id": "app_usage_001", "concept_name": "Practice Session Logging", "summary": "How to track your learning sessions for personalized coaching insights and progress monitoring.", "ics_phase": "app_guidance", "instructions": ["Use the command: log practice [skill_name] [duration_minutes] [optional_notes]", "Example: 'log practice Timeboxing 25 Worked on math homework'", "The coach will track your progress and provide personalized recommendations", "Include difficulty notes to help the coach understand your challenges"], "keywords": ["logging", "practice", "tracking", "progress", "sessions"], "additional_context": "Regular practice logging enables the coach to identify patterns, suggest improvements, and detect when you might be experiencing REBIM (Repetitive Execution Beyond Initial Mastery)."}, {"concept_id": "app_usage_002", "concept_name": "Skill Level Management", "summary": "How to update your competence levels in different learning techniques for accurate coaching.", "ics_phase": "app_guidance", "instructions": ["Use the command: update skill [skill_name] [phase] [level]", "Phases: Enablers, Retrieval, Encoding", "Levels: Unknown, <PERSON><PERSON><PERSON>, Stabilized, Mastered", "Example: 'update skill ActiveRecall Retrieval Stabilized'", "Be honest about your current level for best coaching results"], "keywords": ["skills", "competence", "levels", "assessment", "tracking"], "additional_context": "Accurate skill assessment helps the coach recommend appropriate next steps and avoid suggesting techniques that are too advanced or too basic for your current level."}, {"concept_id": "app_usage_003", "concept_name": "Focus Area Management", "summary": "How to change your main learning focus to align coaching with your current priorities.", "ics_phase": "app_guidance", "instructions": ["Use the command: change focus [area]", "Available areas: Enablers, Retrieval, Encoding", "Example: 'change focus Retrieval'", "Your main focus guides the coach's primary recommendations", "You can still work on other areas, but the coach will emphasize your main focus"], "keywords": ["focus", "priorities", "phases", "coaching", "emphasis"], "additional_context": "The coach uses concurrent phase logic - you'll work on all three phases but with emphasis on your main focus area. Change this as you progress through your learning journey."}, {"concept_id": "app_usage_004", "concept_name": "Distraction and Habit Tracking", "summary": "How to identify and track obstacles that interfere with your learning progress.", "ics_phase": "app_guidance", "instructions": ["Use the command: add distraction [description]", "Example: 'add distraction I check social media when tasks feel difficult'", "Be specific about triggers and patterns", "The coach will suggest mitigation strategies from the knowledge base", "Track progress on overcoming these challenges"], "keywords": ["distractions", "habits", "obstacles", "procrastination", "mitigation"], "additional_context": "Identifying distraction patterns is crucial for developing effective countermeasures. The coach can suggest specific techniques like MVGs or timeboxing to address different types of resistance."}, {"concept_id": "app_usage_005", "concept_name": "Proactive Coaching Features", "summary": "Understanding how the coach initiates conversations and provides guidance throughout your learning journey.", "ics_phase": "app_guidance", "instructions": ["The coach monitors your progress and schedule automatically", "Expect proactive check-ins based on time of day and activity patterns", "The coach will identify when you're not using app features effectively", "Weekly and monthly assessments help adjust your learning roadmap", "Respond honestly to coach questions for best personalization"], "keywords": ["proactive", "monitoring", "check-ins", "assessments", "personalization"], "additional_context": "The coach operates continuously, not just when you ask questions. It analyzes your patterns to identify opportunities for improvement and suggests when you might benefit from different techniques or approaches."}, {"concept_id": "theory_001", "concept_name": "REBIM - Repetitive Execution Beyond Initial Mastery", "summary": "The phenomenon where learners continue using basic techniques after they should progress to more advanced methods.", "ics_phase": "foundational", "instructions": ["Recognize when you're practicing the same technique repeatedly without improvement", "Notice if you feel bored or unchallenged during practice sessions", "Track confidence scores - if they plateau, you may be experiencing REBIM", "Be open to trying higher-order techniques when the coach suggests them", "Remember that difficulty often indicates growth, not failure"], "keywords": ["REBIM", "mastery", "progression", "plateau", "advancement"], "additional_context": "REBIM is a common learning trap where comfort with a technique prevents progression. The coach monitors your practice patterns and confidence scores to detect REBIM and suggest appropriate advancement to higher-order techniques."}, {"concept_id": "theory_002", "concept_name": "Declarative vs Procedural Knowledge", "summary": "Understanding the difference between knowing facts (declarative) and knowing how to do things (procedural).", "ics_phase": "foundational", "instructions": ["Identify whether you're learning facts or skills", "Use different techniques for different knowledge types", "Declarative knowledge benefits from spaced repetition and active recall", "Procedural knowledge requires deliberate practice and application", "Most complex learning involves both types working together"], "keywords": ["declarative", "procedural", "knowledge_types", "facts", "skills"], "additional_context": "Understanding knowledge types helps you choose appropriate learning techniques. The coach considers this when recommending specific approaches for your learning goals."}, {"concept_id": "theory_003", "concept_name": "Growth Mindset vs Fixed Mindset", "summary": "The belief that abilities can be developed (growth) versus the belief that abilities are static (fixed).", "ics_phase": "foundational", "instructions": ["Embrace challenges as opportunities to grow", "View effort as the path to mastery, not a sign of inadequacy", "Learn from criticism and setbacks", "Find inspiration in others' success rather than feeling threatened", "Use 'yet' language: 'I can't do this yet' instead of 'I can't do this'"], "keywords": ["growth_mindset", "fixed_mindset", "challenges", "effort", "learning"], "additional_context": "Your mindset significantly impacts learning effectiveness. The coach helps identify and address fixed mindset patterns that might be limiting your progress."}]